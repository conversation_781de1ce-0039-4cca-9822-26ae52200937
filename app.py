import datetime
import os
from dotenv import load_dotenv

load_dotenv()
import re
from urllib.parse import urlparse
from flask import (
    Flask,
    flash,
    jsonify,
    render_template,
    render_template_string,
    request,
    redirect,
    url_for,
)
from werkzeug.utils import secure_filename
import psycopg2
import boto3
from boto3.session import Session
from botocore.client import Config
import random
from pypdf import PdfReader
import pytesseract
from PIL import Image
import io
import pdf2image
import redmail
import json
import feedparser
import feedgenerator
import pycountry


# Initialize S3 client
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
PG_HOST = os.getenv("PGHOST")
PG_DATABASE = os.getenv("PGDATABASE")
PG_USER = os.getenv("PGUSER")
PG_PASSWORD = os.getenv("PGPASSWORD")

s3_session = boto3.session.Session()
client = s3_session.client(
    "s3",
    region_name="fra1",
    endpoint_url="https://fra1.digitaloceanspaces.com",
    aws_access_key_id=AWS_ACCESS_KEY_ID,
    aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
)

app = Flask(__name__)
app.secret_key = "super secret key"

# my_key_manager = APIKeyManager(app)
# my_key = my_key_manager.create('MY_FIRST_KEY')
# print(my_key.secret)

ALLOWED_EXTENSIONS = {"pdf"}
MAX_FILE_SIZE = 3 * 1024 * 1024  # 3MB

# Phone number validation pattern
PHONE_REGEX = re.compile(r"^\+?[1-9]\d{1,14}$")  # E.164 format

# Email validation pattern - more robust than basic regex
EMAIL_REGEX = re.compile(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")

# Common fake email domains to block
FAKE_EMAIL_DOMAINS = {
    "example.com", "test.com", "fake.com", "invalid.com", "dummy.com",
    "temp.com", "temporary.com", "disposable.com", "throwaway.com",
    "mailinator.com", "10minutemail.com", "guerrillamail.com"
}


def is_valid_email(email):
    """
    Validate email address with basic checks to prevent fake/bot emails
    """
    if not email or not isinstance(email, str):
        return False

    email = email.strip().lower()

    # Basic format validation
    if not EMAIL_REGEX.match(email):
        return False

    # Check for fake domains
    domain = email.split('@')[1] if '@' in email else ''
    if domain in FAKE_EMAIL_DOMAINS:
        return False

    # Check for suspicious patterns
    if email.count('@') != 1:
        return False

    # Check for minimum length
    if len(email) < 5:
        return False

    return True


# Helper function for file validation
def allowed_file(filename):
    return "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS


@app.errorhandler(404)
def page_not_found(e):
    return render_template("404.html"), 404


def collect_rss_vacancies_we_work_remotely(keyword=""):
    # keyword is a string in the format of "%keyword%"
    feed_url = f"https://weworkremotely.com/remote-jobs.rss"
    feed = feedparser.parse(feed_url)

    feed.entries = [
        item
        for item in feed.entries
        if keyword[1:-1].lower() in item.title.split(": ")[1].lower()
    ]

    vacancies = []
    for item in feed.entries:
        try:
            vacancy = {
                "title": item.title.split(": ")[1],
                "company": item.title.split(": ")[0],
                "description": item["summary"],
                "link": item.link,
                "published": item.published,
                "region": item.get("region", ""),
                "category": item.get("category", ""),
                "type": item.get("type", ""),
                "logo_url": item.get("media_content", [{}])[0].get("url", ""),
                "expires_at": item.get("expires_at", ""),
            }
            vacancies.append(vacancy)
        except:
            continue

    # Convert vacancies to match our typical vacancy structure
    converted_vacancies = []
    for vacancy in vacancies:
        try:
            converted_vacancy = {
                "vacancy_id": f"wwr_{hash(vacancy['link'])}",  # Generate a unique ID
                "vacancy_title": vacancy["title"],
                "employer_name": vacancy["company"],
                "vacancy_job_description": vacancy["description"]
                .split("</a>\n</p>\n")[1]
                .split("<strong>To apply:</strong> <a href=")[0],
                "vacancy_url": vacancy["link"],
                "company_url": vacancy["description"]
                .split("<strong>URL:</strong> <a href=")[1]
                .split(">")[1]
                .split("<")[0],
                "vacancy_creation_date": vacancy["published"],
                "vacancy_country": vacancy["region"],
                "vacancy_city": "",  # We don't have this information from the RSS feed
                "jobtags": [],
                "employer_logo_url": vacancy["logo_url"],
                "vacancy_status": "Active",
                "salary_min": "Unspecified",  # We don't have salary information from the RSS feed
                "salary_max": "",
                "salary_currency": "Unknown",
                "vacancy_type": vacancy["type"],
                "office_schedule": "Remote",  # We don't have this information from the RSS feed
                "vacancy_experience": "",  # We don't have this information from the RSS feed
                "vacancy_education": "",  # We don't have this information from the RSS feed
                "listing_source": "WeWorkRemotely",
                "employer_banner_url": "",
            }
            if converted_vacancy["employer_logo_url"] == "":
                continue
            else:
                converted_vacancies.append(converted_vacancy)
        except:
            continue

    return converted_vacancies


def send_app_confirmation_email(
    recipient,
    fname,
    vacancy_id,
    application_id,
    employer_id,
    employer_name,
    vacancy_title,
):
    print("Sending email", flush=True)
    email_subject_key = f"#V{vacancy_id}E{employer_id}A{application_id}"
    subject = f"Application Confirmation for {vacancy_title} at {employer_name} - {email_subject_key}"
    body = f"Dear {fname},\n\n Thank you for applying for the {vacancy_title} position at {employer_name}.\n\n We will review your application and get back to you soon.\n\nRegards,\n {employer_name}"
    sender = os.getenv("MAIL_USERNAME")
    # Send email using redmail
    try:
        r_email = redmail.EmailSender(
            host=os.getenv("SMTP_MAIL_HOST"),
            port=os.getenv("SMTP_MAIL_PORT"),
            username=sender,
            password=os.getenv("MAIL_PASSWORD"),
        )
        r_email.connect()
        r_email.send(
            subject=f"{subject} - {email_subject_key}",
            sender=f"{employer_name} <{sender}>",
            text=body,
            receivers=recipient,
        )
    except Exception as e:
        print(f"Error sending email: {str(e)}", flush=True)
    pass


@app.route("/")
def index():

    conn, cur = ats_safe_connect()
    cur.execute(
        """SELECT
        * FROM highlighted_jobs
        WHERE salary_max > 0
        AND vacancy_status = 'Active'
        ORDER BY vacancy_creation_date DESC
        LIMIT 4;"""
    )
    columns = [col[0] for col in cur.description]
    recent_remote_jobs = [dict(zip(columns, row)) for row in cur.fetchall()]

    # Get highest paying jobs
    cur.execute(
        """SELECT
        * FROM highlighted_jobs
        WHERE salary_max > 0
        AND vacancy_status = 'Active'
        ORDER BY
        salary_max DESC
        LIMIT 4;"""
    )
    columns = [col[0] for col in cur.description]
    highest_paying_jobs = [dict(zip(columns, row)) for row in cur.fetchall()]

    return render_template(
        "index.html",
        recent_remote_jobs=recent_remote_jobs,
        highest_paying_jobs=highest_paying_jobs,
    )


@app.route("/employer-signup", methods=["GET", "POST"])
def employer_signup():
    if request.method == "POST":
        # Get form data
        company_name = request.form.get("companyName", "").strip()
        contact_name = request.form.get("contactName", "").strip()
        email = request.form.get("email", "").strip()
        phone = request.form.get("phone", "").strip()
        website = request.form.get("website", "").strip()
        verification_info = request.form.get("verificationInfo", "").strip()
        agreement = request.form.get("agreement") == "on"

        # Validate form data
        errors = []

        # Company Name validation
        if len(company_name) < 2 or len(company_name) > 100:
            errors.append("Company name must be between 2-100 characters")

        # Contact Person validation
        if len(contact_name) < 2 or len(contact_name) > 50:
            errors.append("Contact name must be between 2-50 characters")

        # Email validation
        if not is_valid_email(email):
            errors.append("Please enter a valid work email address")

        # Phone validation (using your existing pattern)
        if not PHONE_REGEX.match(phone):
            errors.append("Please enter a valid international phone number")

        # Website validation
        try:
            result = urlparse(website)
            if not all([result.scheme, result.netloc]):
                errors.append("Please enter a valid website URL")
        except:
            errors.append("Please enter a valid website URL")

        # Agreement check
        if not agreement:
            errors.append("You must agree to post only genuine job opportunities")

        if errors:
            for error in errors:
                flash(error, "danger")
            return redirect(url_for("employer_signup"))

        try:
            # Database operations
            conn, cur = ats_safe_connect()
            cur.execute(
                """INSERT INTO feed_potentialemployer
                (company_name, contact_name, email, phone, website,
                 verification_info, created_at, status)
                VALUES (%s, %s, %s, %s, %s, %s, NOW(), %s)""",
                (
                    company_name,
                    contact_name,
                    email,
                    phone,
                    website,
                    verification_info,
                    "Pending",  # Status
                ),
            )
            conn.commit()
            cur.close()

            flash(
                "Registration submitted successfully! We'll contact you for verification within 2 business days.",
                "success",
            )

            # Send confirmation email (pseudo-code)
            send_emp_verification_email(email, company_name)

            return redirect(url_for("employer_signup"))

        except Exception as e:
            app.logger.error(f"Employer registration error: {str(e)}")
            flash("An error occurred during registration. Please try again.", "danger")
            return redirect(url_for("employer_signup"))

    # GET request - show form
    return render_template("register_company.html")


def send_emp_verification_email(email, company_name):
    # Pseudo-code to send email
    print(f"Sending verification email to {email} for {company_name}")
    # Implement your email sending logic here
    # For example, using Flask-Mail or any other email service
    pass


@app.route("/talent-pool", methods=["GET", "POST"])
def talent_pool():
    if request.method == "POST":
        # Get form data
        first_name = request.form.get("firstName", "").strip()
        last_name = request.form.get("lastName", "").strip()
        phone = request.form.get("phone", "").strip()
        email = request.form.get("email", "").strip()
        location = request.form.get("location", "").strip()
        cv_file = request.files.get("cvUpload")

        # Validate form data
        errors = []

        # Validate first and last name
        if len(first_name) < 2 or len(first_name) > 50:
            errors.append("First name must be between 2-50 characters")
        if len(last_name) < 2 or len(last_name) > 50:
            errors.append("Last name must be between 2-50 characters")

        # Validate phone number (optional)
        if phone and not PHONE_REGEX.match(phone):
            errors.append("Please enter a valid international phone number")

        # Validate email
        if not is_valid_email(email):
            errors.append("Please enter a valid email address")

        # Validate location
        if not location:
            errors.append("Please select your country of residence")

        # Validate CV file
        if not cv_file or cv_file.filename == "":
            errors.append("Please select a CV file to upload")

        if errors:
            for error in errors:
                flash(error, "danger")
            return redirect(url_for("talent_pool"))

        try:
            # Save the CV in a safe and accessible location
            if cv_file.filename != "":
                cv_name = secure_filename(f"{email}_.pdf")
                cv_name = f"{os.urandom(8).hex()}_{cv_name}"
                local_cv_location = "./cv_storage/{}".format(cv_name)
                cloud_cv_location = "{}/{}".format("candidate-pool", cv_name)
                cv_file.save(local_cv_location)
                client.upload_file(
                    local_cv_location, "applicants-cv-bucket", cloud_cv_location
                )
                if os.path.exists(local_cv_location):
                    os.remove(local_cv_location)
                else:
                    print("The file does not exist")

            # Insert data into the feed_talentpool table
            conn, cur = ats_safe_connect()
            cur.execute(
                """INSERT INTO feed_talentpool
                (talent_firstname, talent_lastname, talent_phone, talent_email,
                 talent_country, cv_location, talent_status, talent_added_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s);""",
                (
                    first_name,
                    last_name,
                    phone,
                    email,
                    location,
                    cloud_cv_location,
                    "Active",
                    "NOW()",
                ),
            )
            conn.commit()
            cur.close()

            flash(
                "Successfully registered in the talent pool! Employers may contact you soon.",
                "success",
            )
            return redirect(url_for("talent_pool"))

        except Exception as e:
            app.logger.error(f"Error processing registration: {str(e)}")
            flash("An error occurred during registration. Please try again.", "danger")
            return redirect(url_for("talent_pool"))

    # GET request - show form
    return render_template("talent_pool.html")


@app.route("/about")
def about():
    return render_template("about.html")


@app.route("/jobs")
def all_jobs():

    we_work_remotely_vacancies = collect_rss_vacancies_we_work_remotely()

    conn, cur = ats_safe_connect()

    query = "SELECT * FROM vacancies WHERE vacancy_status = 'Active' LIMIT 200;"

    try:
        cur.execute(query)
    except:
        conn, cur = ats_safe_connect()
        cur.execute(query)

    columns = [col[0] for col in cur.description]
    vacancies = [dict(zip(columns, row)) for row in cur.fetchall()]

    cur.close()

    # Combine the converted vacancies with our existing vacancies
    vacancy_list = vacancies + we_work_remotely_vacancies

    all_tags = {}
    for vac in vacancy_list:
        try:
            vac["listing_source"]
            vac["listing_source"] = vac["listing_source"]
        except:
            vac["listing_source"] = "Canvider"

        if vac["jobtags"] is not None:
            try:
                all_tags[vac["vacancy_id"]] = json.loads(vac["jobtags"])
            except:
                all_tags[vac["vacancy_id"]] = []
        else:
            all_tags[vac["vacancy_id"]] = []

    query = "SELECT * FROM all_job_locations"

    try:
        cur.execute(query)
    except:
        conn, cur = ats_safe_connect()
        cur.execute(query)

    columns = [col[0] for col in cur.description]
    locations = [dict(zip(columns, row)) for row in cur.fetchall()]

    cur.close()

    return render_template(
        "jobs.html",
        all_vacancies=vacancy_list,
        all_tags=all_tags,
        all_locations=locations,
    )


# Route of the vacancy description and application form
@app.route("/apply/<vacancy_id>")
def view_vacancy(vacancy_id):
    # Get source parameter from URL if present
    source = request.args.get("source", "direct")
    if source == "direct":
        source = "workloupe"

    conn, cur = ats_safe_connect()

    cur.execute(
        "SELECT * FROM vacancies WHERE vacancy_id = {} LIMIT 1".format(vacancy_id)
    )
    columns = [col[0] for col in cur.description]
    vacancy = [dict(zip(columns, row)) for row in cur.fetchall()][0]

    cur.close()

    all_tags = {}
    if vacancy["jobtags"] is not None:
        try:
            tags = json.loads(vacancy["jobtags"])
            all_tags[vacancy["vacancy_id"]] = tags
        except:
            all_tags[vacancy["vacancy_id"]] = []
    else:
        all_tags[vacancy["vacancy_id"]] = []

    if vacancy["vacancy_status"] == "Active":
        return render_template(
            "vacancy.html", vacancy=vacancy, all_tags=all_tags, source=source
        )
    else:
        return render_template(
            "vacancy_closed.html", vacancy=vacancy, all_tags=all_tags, source=source
        )


# Route to handle form submission
@app.route("/submit", methods=["POST"])
def submit():
    print("Submitting application", flush=True)
    # Collect data from the form
    fname = request.form.get("fname", "").strip()
    lname = request.form.get("lname", "").strip()
    email = request.form.get("email", "").strip()
    phone = request.form.get("phone", "").strip()
    notice = request.form.get("notice", "").strip()
    applicant_cv = request.files.get("cv")
    applied_vacancy_id = request.form.get("vac_id", "").strip()
    source = request.form.get("source", "").strip()

    # Validate form data
    errors = []

    # Validate first name
    if not fname or len(fname) < 2 or len(fname) > 50:
        errors.append("First name must be between 2-50 characters")

    # Validate last name
    if not lname or len(lname) < 2 or len(lname) > 50:
        errors.append("Last name must be between 2-50 characters")

    # Validate email with enhanced validation
    if not email:
        errors.append("Email address is required")
    elif not is_valid_email(email):
        errors.append("Please enter a valid email address")

    # Validate phone number (optional but if provided, must be valid)
    if phone and not PHONE_REGEX.match(phone):
        errors.append("Please enter a valid international phone number")

    # Validate CV file
    if not applicant_cv or applicant_cv.filename == "":
        errors.append("Please upload your CV/resume")
    elif not allowed_file(applicant_cv.filename):
        errors.append("Please upload a PDF file for your CV/resume")

    # Validate vacancy ID
    if not applied_vacancy_id:
        errors.append("Invalid job application")

    # If there are validation errors, redirect back with error messages
    if errors:
        for error in errors:
            flash(error, "danger")
        return redirect(url_for("view_vacancy", vacancy_id=applied_vacancy_id))

    print(f"Application validation passed for email: {email}", flush=True)

    # Save the CV in safe and accessible location
    cv_text = ""
    if applicant_cv.filename != "":
        cv_name = secure_filename("{}_.pdf".format(email))
        local_cv_location = "./cv_storage/{}".format(cv_name)
        cloud_cv_location = "{}/{}".format(applied_vacancy_id, cv_name)
        applicant_cv.save(local_cv_location)
        try:
            cv_text = extract_text_from_pdf(local_cv_location)
        except Exception as e:
            print(f"Error extracting text from PDF: {str(e)}", flush=True)
            cv_text = "Error extracting text"

        try:
            client.upload_file(
                local_cv_location, "applicants-cv-bucket", cloud_cv_location
            )
            # Delete the local file after uploading
            if os.path.exists(local_cv_location):
                os.remove(local_cv_location)
            else:
                print("The resume file did not exist...", flush=True)
        except Exception as e:
            print(f"Error uploading file: {str(e)}", flush=True)

    # Insert candidate
    try:
        conn, cur = ats_safe_connect()
        def_adress = "Place, Holder"
        def_date_of_birth = "1111-01-01"

        # Generate a random number between 10 and 60 for dark avatar background
        rand_a, rand_b, rand_c = (
            random.randint(10, 60),
            random.randint(10, 60),
            random.randint(10, 60),
        )
        def_avatar_bg_color = "#{}{}{}".format(str(rand_a), str(rand_b), str(rand_c))

        cur.execute(
            "INSERT INTO feed_candidate (candidate_firstname, candidate_lastname, candidate_email, candidate_phone, candidate_address, candidate_date_of_birth, avatar_bg_color, candidate_created_at) VALUES (%s, %s, %s, %s, %s, %s, %s, NOW()) RETURNING candidate_id;",
            (
                fname,
                lname,
                email,
                phone,
                def_adress,
                def_date_of_birth,
                def_avatar_bg_color,
            ),
        )
        candidate_id = cur.fetchone()[0]
        conn.commit()
        cur.close()
    except Exception as e:
        print(f"Error inserting candidate: {str(e)}", flush=True)
        flash(
            "An error occurred during application submission. Please try again.",
            "danger",
        )
        return redirect(url_for("view_vacancy", vacancy_id=applied_vacancy_id))

    # Insert application
    try:
        conn, cur = ats_safe_connect()
        application_source = str(source).lower()
        application_status = "Active"
        application_state = "New"
        current_employer = "Unknown"
        current_position = "Unknown"
        education_level = "Unknown"
        total_exp_years = 0

        cur.execute(
            """ INSERT INTO feed_application (
                                    candidate_id,
                                    vacancy_id,
                                    application_date,
                                    application_source,
                                    application_status,
                                    application_state,
                                    current_employer,
                                    current_position,
                                    education_level,
                                    notice_period,
                                    total_exp_years,
                                    score,
                                    cv_location
                                    ) VALUES (%s,%s, NOW(),%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) RETURNING application_id; """,
            (
                candidate_id,
                applied_vacancy_id,
                application_source,
                application_status,
                application_state,
                current_employer,
                current_position,
                education_level,
                notice,
                total_exp_years,
                -1,
                cloud_cv_location,
            ),
        )
        application_id = cur.fetchone()[0]
        conn.commit()
        cur.close()
    except Exception as e:
        print(f"Error inserting application: {str(e)}", flush=True)
        flash(
            "An error occurred during application submission. Please try again.",
            "danger",
        )
        return redirect(url_for("view_vacancy", vacancy_id=applied_vacancy_id))

    # Insert application state
    try:
        conn, cur = ats_safe_connect()
        cur.execute(
            """ INSERT INTO feed_applicationstate (
                                    state_name,
                                    state_notes,
                                    state_started_at,
                                    application_id,
                                    committed_by_id)
                                    VALUES (%s,%s,NOW(),%s,%s); """,
            (
                application_state,
                "Application submitted from the workloupe.com",
                application_id,
                "1",
            ),
        )
        conn.commit()
        cur.close()
    except Exception as e:
        print(f"Error inserting application state: {str(e)}", flush=True)

    # Insert CV text
    try:
        conn, cur = ats_safe_connect()
        cur.execute(
            """
        INSERT INTO feed_applicationcvtext(
                    application_id,
                    cv_text,
                    cv_text_date,
                    is_cv_analyzed,
                    cv_location)
                    VALUES (%s,%s,NOW(),%s,%s);
        """,
            (
                application_id,
                cv_text,
                False,
                cloud_cv_location,
            ),
        )
        conn.commit()
        cur.close()
    except Exception as e:
        print(f"Error inserting CV text: {str(e)}")

    # Send confirmation email
    try:
        # Get employer ID based on feed_vacancy (it has employer_id)
        conn, cur = ats_safe_connect()
        cur.execute(
            "SELECT employer_id, vacancy_title FROM feed_vacancy WHERE vacancy_id = %s;",
            (applied_vacancy_id,),
        )
        employer_id, vacancy_title = cur.fetchone()
        cur.close()

        # Get employer name based on employer_id
        conn, cur = ats_safe_connect()
        cur.execute(
            "SELECT employer_name FROM feed_employer WHERE employer_id = %s;",
            (employer_id,),
        )
        employer_name = cur.fetchone()[0]
        cur.close()

        send_app_confirmation_email(
            recipient=email,
            fname=fname,
            vacancy_id=applied_vacancy_id,
            application_id=application_id,
            employer_id=employer_id,
            employer_name=employer_name,
            vacancy_title=vacancy_title,
        )
    except Exception as e:
        print(f"Error sending email: {str(e)}")

    # Check if the candidate is already in the talent pool
    try:
        conn, cur = ats_safe_connect()
        cur.execute("SELECT * FROM feed_talentpool WHERE talent_email = %s;", (email,))
        in_talent_pool = cur.fetchone() is not None
        cur.close()

        if in_talent_pool:
            flash("Successfully applied for the job!", "success")
            return redirect(url_for("view_vacancy", vacancy_id=applied_vacancy_id))
        else:
            flash(
                "Successfully applied for the job! Consider joining our talent pool to attract interest from the employers.",
                "success",
            )
            return redirect(url_for("talent_pool"))
    except Exception as e:
        print(f"Error checking talent pool: {str(e)}")
        flash("Application submitted successfully!", "success")
        return redirect(url_for("view_vacancy", vacancy_id=applied_vacancy_id))


@app.route("/employers")
def employers():
    conn, cur = ats_safe_connect()

    cur.execute("SELECT * FROM employer_cards ORDER BY open_positions DESC LIMIT 50;")
    columns = [col[0] for col in cur.description]
    all_employers = [dict(zip(columns, row)) for row in cur.fetchall()]

    cur.close()
    return render_template("employers.html", all_employers=all_employers)


@app.route("/employers/<employer_id>")
def employer(employer_id):

    conn, cur = ats_safe_connect()

    cur.execute(
        "SELECT * FROM feed_employer WHERE employer_id::integer = {} LIMIT 1;".format(
            employer_id
        )
    )
    columns = [col[0] for col in cur.description]
    employer = [dict(zip(columns, row)) for row in cur.fetchall()][0]

    social_portals = []
    for i in str(employer["employer_social_portals"]).split(","):
        i = i.strip()
        splitted = i.split(";")
        portal, url = splitted[0], splitted[1]
        social_portals.append({"url": url, "location": portal})

    locations = []
    ## Make popover in future.
    office_count = 0
    for i in str(employer["office_locations"]).split("|"):
        if office_count > 4:
            locations.append({"location": "+ More Locations"})
            break
        else:
            i = i.strip()
            office_count = office_count + 1
            locations.append({"location": i})

    all_photos = client.list_objects_v2(
        Bucket="canvider-public", Prefix=f"company-galleries/{employer_id}/"
    )
    try:
        gallery = [i["Key"] for i in all_photos["Contents"][1:]]
    except:
        gallery = []

    cur.execute(
        "SELECT * FROM vacancies WHERE employer_id::integer = {}".format(employer_id)
    )
    columns = [col[0] for col in cur.description]
    vacancies = [dict(zip(columns, row)) for row in cur.fetchall()]

    cur.close()

    try:
        return render_template(
            "company.html",
            employer=employer,
            vacancies=vacancies,
            social_portals=social_portals,
            locations=locations,
            all_gallery=gallery,
        )
    except:
        return render_template_string("404")


def ats_safe_connect():
    conn = psycopg2.connect(dsn=os.getenv("DATABASE_URL"))

    try:
        conn.autocommit = False  # Explicit transactions
        cur = conn.cursor()
    except:
        print("Reconnecting to the database...")
        ats_safe_connect()

    return conn, cur


def extract_text_from_pdf(file_path):
    text = ""
    try:
        # First try with PyPDF
        reader = PdfReader(file_path)
        for page in reader.pages:
            page_text = page.extract_text()
            text += page_text if page_text else ""

        # If PyPDF returned empty or very little text, try OCR
        if len(text.strip()) < 50:  # Adjust threshold as needed
            print("PyPDF extraction yielded little text, trying OCR...", flush=True)
            images = pdf2image.convert_from_path(file_path)
            ocr_text = ""
            for img in images:
                ocr_text += pytesseract.image_to_string(img)

            # Use OCR result if it's better than PyPDF
            if len(ocr_text.strip()) > len(text.strip()):
                text = ocr_text
    except Exception as e:
        print(f"Error extracting text from PDF: {str(e)}", flush=True)

    return text


@app.route("/filterjobs")
def filter_jobs():
    query = request.args.get("keyword-input", "").lower()
    query = f"%{query}%"

    query_loc = request.args.get("loc-select", "").split(",")

    try:
        query_city, query_country = (
            f"%{query_loc[0].strip()}%",
            f"%{query_loc[1].strip()}%",
        )
    except:
        query_city, query_country = f"%%", f"%%"

    conn, cur = ats_safe_connect()

    cur.execute(
        """
        SELECT
            *
        FROM vacancies
        WHERE (vacancy_title ILIKE %s OR employer_name ILIKE %s)
          AND (vacancy_country ILIKE %s AND vacancy_city ILIKE %s)
        ORDER BY vacancy_id ASC
        LIMIT 200;
        """,
        (query, query, query_country, query_city),
    )

    columns = [col[0] for col in cur.description]
    vacancies = [dict(zip(columns, row)) for row in cur.fetchall()]

    cur.close()

    we_work_remotely_vacancies = collect_rss_vacancies_we_work_remotely(keyword=query)

    all_tags = {}
    for vac in vacancies:
        try:
            vac["listing_source"]
            vac["listing_source"] = vac["listing_source"]
        except:
            vac["listing_source"] = "Canvider"
        if vac["jobtags"] is not None:
            try:
                all_tags[vac["vacancy_id"]] = json.loads(vac["jobtags"])
            except:
                all_tags[vac["vacancy_id"]] = []
        else:
            all_tags[vac["vacancy_id"]] = []

    vacancy_list = vacancies + we_work_remotely_vacancies
    return render_template(
        "jobs_list.html", all_vacancies=vacancy_list, all_tags=all_tags
    )


@app.route("/filteremp")
def filter_employers():
    query = request.args.get("keyword-input", "").lower()
    query = f"%{query}%"

    conn, cur = ats_safe_connect()

    cur.execute(
        """
    SELECT *
    FROM employer_cards
    WHERE employer_name ILIKE %s OR employer_industry ILIKE %s OR headquarter ILIKE %s
    ORDER BY open_positions DESC
    LIMIT 50;
    """,
        (
            query,
            query,
            query,
        ),
    )

    columns = [col[0] for col in cur.description]
    filtered_employers = [dict(zip(columns, row)) for row in cur.fetchall()]

    return render_template("employers_list.html", all_employers=filtered_employers)


class ExtendedRSSFeed(feedgenerator.Rss201rev2Feed):
    def add_item(self, **kwargs):
        # Extract custom fields
        employer_name = kwargs.pop("employer_name", None)
        country = kwargs.pop("country", None)
        city = kwargs.pop("city", None)
        salary_min = kwargs.pop("salary_min", None)
        salary_max = kwargs.pop("salary_max", None)
        salary_currency = kwargs.pop("salary_currency", None)
        office_schedule = kwargs.pop("office_schedule", None)
        work_mode = kwargs.pop("work_mode", None)
        skills = kwargs.pop("skills", None)

        # Call parent add_item with standard fields
        item = super(ExtendedRSSFeed, self).add_item(**kwargs)

        # Add custom elements to the last item (the one we just added)
        if employer_name:
            self.items[-1]["employer_name"] = employer_name
        if country:
            self.items[-1]["country"] = country
        if city:
            self.items[-1]["city"] = city
        if salary_min:
            self.items[-1]["salary_min"] = salary_min
        if salary_max:
            self.items[-1]["salary_max"] = salary_max
        if salary_currency:
            self.items[-1]["salary_currency"] = salary_currency
        if office_schedule:
            self.items[-1]["office_schedule"] = office_schedule
        if work_mode:
            self.items[-1]["work_mode"] = work_mode
        if skills:
            self.items[-1]["skills"] = skills

        return item

    def write(self, outfile, encoding):
        # Add custom namespace
        handler = feedgenerator.SimplerXMLGenerator(outfile, encoding)
        handler.startDocument()
        handler.startElement("rss", self.rss_attributes())
        handler.startElement("channel", self.root_attributes())
        self.add_root_elements(handler)
        self.write_items(handler)
        self.endChannelElement(handler)
        handler.endElement("rss")

    def write_items(self, handler):
        for item in self.items:
            handler.startElement("item", {})

            # Add standard elements
            self.add_item_elements(handler, item)

            # Add custom elements with workloupe namespace
            if "employer_name" in item:
                handler.addQuickElement("employer", item["employer_name"])
            if "country" in item:
                handler.addQuickElement("country", item["country"])
            if "city" in item and item["city"]:
                handler.addQuickElement("city", item["city"])
            if "salary_min" in item:
                handler.addQuickElement("salary_min", str(item["salary_min"]))
            if "salary_max" in item:
                handler.addQuickElement("salary_max", str(item["salary_max"]))
            if "salary_currency" in item:
                handler.addQuickElement("salary_currency", item["salary_currency"])
            if "office_schedule" in item:
                handler.addQuickElement("office_schedule", item["office_schedule"])
            if "work_mode" in item:
                handler.addQuickElement("work_mode", item["work_mode"])
            if "skills" in item and item["skills"]:
                handler.addQuickElement("skills", item["skills"])

            handler.endElement("item")

    def rss_attributes(self):
        attrs = super(ExtendedRSSFeed, self).rss_attributes()
        attrs["xmlns:workloupe"] = "http://workloupe.com/ns/rss/1.0/"
        return attrs


@app.route("/<employer_name>/jobfeed.rss")
def jobfeed(employer_name):
    conn, cur = ats_safe_connect()

    cur.execute(
        """
        SELECT
            *
        FROM feed_employer
        WHERE employer_name = %s
        """,
        (employer_name,),
    )

    columns = [col[0] for col in cur.description]
    employer = [dict(zip(columns, row)) for row in cur.fetchall()][0]

    cur.close()

    conn, cur = ats_safe_connect()

    cur.execute(
        """
        SELECT
            *
        FROM feed_vacancy
        WHERE employer_id = %s AND vacancy_status = 'Active'
        ORDER BY vacancy_id DESC
        LIMIT 200;
        """,
        (str(employer["employer_id"]),),
    )

    columns = [col[0] for col in cur.description]
    vacancies = [dict(zip(columns, row)) for row in cur.fetchall()]

    cur.close()

    employer_name = employer["employer_name"]

    xml_output = '<?xml version="1.0" encoding="utf-8"?>\n<source>'

    for vacancy in vacancies:
        salary = (
            f"{vacancy['salary_min']}-{vacancy['salary_max']} {vacancy['salary_currency']}"
            if vacancy["salary_min"] and vacancy["salary_max"]
            else "Not specified"
        )
        location = (
            f"{vacancy['vacancy_city']},{vacancy['vacancy_country']}"
            if vacancy["vacancy_city"]
            else vacancy["vacancy_country"]
        )

        # Clean HTML tags and entities and remove a tags to not have empty links on the raw description and remove emojis.
        description_without_markups = vacancy["vacancy_job_description"]

        # Remove Emojis
        description_without_markups = description_without_markups.encode(
            "ascii", "ignore"
        ).decode("ascii")

        # remove a tags and anything inside of them to not have empty links on the raw description.
        description_without_markups = re.sub(
            "<a[^>]*>(.*?)</a>", "", description_without_markups
        )

        # Add spacing for headers with attribute support
        description_without_markups = re.sub(
            "<h1[^>]*>(.*?)</h1>", "\n\n\\1\n\n", description_without_markups
        )
        description_without_markups = re.sub(
            "<h2[^>]*>(.*?)</h2>", "\n\n\\1\n\n", description_without_markups
        )
        description_without_markups = re.sub(
            "<h3[^>]*>(.*?)</h3>", "\n\\1\n", description_without_markups
        )

        # Add spacing for paragraphs with attribute support
        description_without_markups = re.sub(
            "<p[^>]*>(.*?)</p>", "\\1\n\n", description_without_markups
        )

        # Add spacing for lists with attribute support
        description_without_markups = re.sub(
            "<li[^>]*>(.*?)</li>", "\n * \\1", description_without_markups
        )
        description_without_markups = re.sub(
            "<ul[^>]*>", "\n", description_without_markups
        )
        description_without_markups = re.sub(
            "</ul>", "\n\n", description_without_markups
        )
        description_without_markups = re.sub(
            "<ol[^>]*>", "\n", description_without_markups
        )
        description_without_markups = re.sub(
            "</ol>", "\n\n", description_without_markups
        )

        # Replace HTML space entities with actual spaces
        description_without_markups = description_without_markups.replace("&nbsp;", " ")

        # Remove remaining HTML tags and entities
        description_without_markups = re.sub("<[^>]+>", "", description_without_markups)
        description_without_markups = re.sub(
            "&lt;.*?&gt;", "", description_without_markups
        )
        description_without_markups = description_without_markups.strip()

        # Replace multiple spaces with single space and preserve line breaks
        description_clean_spaces = re.sub("[ \t]+", " ", description_without_markups)

        # Add proper line breaks for sections and preserve existing spacing
        clean_description = description_clean_spaces.replace(". ", ".\n\n").replace(
            "\n\n\n", "\n\n"
        )

        # Handle common section headers by ensuring they have space after them
        clean_description = re.sub(r"([A-Za-z]+):", r"\1: ", clean_description)

        # Ensure there's space between sentences that don't end with periods
        clean_description = re.sub(r"([a-z])([A-Z])", r"\1 \2", clean_description)

        # ensure that the first letter is capitalized
        clean_description = clean_description[0].upper() + clean_description[1:]

        # Ensure that the standard is UTF-8
        clean_description = clean_description.encode("utf-8", "ignore").decode("utf-8")

        xml_output += f"""
        <job>
            <title>{vacancy['vacancy_title']}</title>
            <date>{vacancy['vacancy_creation_date']}</date>
            <referencenumber>{vacancy['vacancy_id']}</referencenumber>
            <url>https://workloupe.com/apply/{vacancy['vacancy_id']}?source=postjobfree</url>
            <company>{employer_name}</company>
            <location>{location}</location>
            <description>{clean_description}</description>
            <salary>{salary}</salary>
            <email><EMAIL></email>
        </job>"""

    xml_output += "\n</source>"

    return xml_output, 200, {"Content-Type": "application/rss+xml"}


@app.route("/all_positions.rss")
def all_positions_rss():
    conn, cur = ats_safe_connect()

    # Get all active vacancies
    cur.execute(
        """
        SELECT v.vacancy_id, v.vacancy_title, v.vacancy_creation_date, v.vacancy_job_description, v.salary_min, v.salary_max, v.salary_currency, v.vacancy_city, v.vacancy_country, e.employer_name
        FROM feed_vacancy v
        JOIN feed_employer e ON v.employer_id::text = e.employer_id::text
        WHERE vacancy_status = 'Active'
        ORDER BY vacancy_id DESC
        LIMIT 1000;
        """
    )
    vacancies = [
        dict(zip([col[0] for col in cur.description], row)) for row in cur.fetchall()
    ]

    # Create XML response
    xml_output = """<?xml version="1.0" encoding="utf-8"?>
<source>
    <publisher>Workloupe</publisher>
    <publisherurl>https://workloupe.com</publisherurl>
    <timezone>UTC</timezone>
    <lastBuildDate>{}</lastBuildDate>""".format(
        datetime.datetime.now().strftime("%m/%d/%Y %I:%M:%S %p")
    )

    for vacancy in vacancies:
        # Get employer info
        xml_output += f"""
    <job>
        <title><![CDATA[{vacancy["vacancy_title"]}]]></title>
        <date><![CDATA[{vacancy["vacancy_creation_date"]}]]></date>
        <referencenumber><![CDATA[{vacancy["vacancy_id"]}]]></referencenumber>
        <url><![CDATA[https://workloupe.com/apply/{vacancy["vacancy_id"]}?source=feed]]></url>
        <company><![CDATA[{vacancy["employer_name"]}]]></company>
        <city><![CDATA[{vacancy["vacancy_city"]}]]></city>
        <country><![CDATA[{vacancy["vacancy_country"]}]]></country>
        <description><![CDATA[{vacancy["vacancy_job_description"]}]]></description>
        <salary><![CDATA[{vacancy["salary_min"]} - {vacancy["salary_max"]} {vacancy["salary_currency"]}]]></salary>
        <email><![CDATA[<EMAIL>]]></email>
    </job>"""

    xml_output += "\n</source>"

    cur.close()
    return xml_output, 200, {"Content-Type": "application/rss+xml"}


@app.route("/<employer_name>/jobs.rss")
def xml_feed(employer_name):
    ## workloupe based on workable style. It is not the same as the jobfeed.rss.
    conn, cur = ats_safe_connect()

    # Get employer info
    cur.execute(
        """SELECT * FROM feed_employer WHERE employer_name = %s""", (employer_name,)
    )
    employer = dict(zip([col[0] for col in cur.description], cur.fetchone()))

    # Get active vacancies for employer
    cur.execute(
        """SELECT * FROM feed_vacancy
           WHERE employer_id = %s AND vacancy_status = 'Active'
           ORDER BY vacancy_creation_date DESC""",
        (str(employer["employer_id"]),),
    )
    vacancies = [
        dict(zip([col[0] for col in cur.description], row)) for row in cur.fetchall()
    ]

    # Create XML response
    xml_output = f"""<?xml version="1.0" encoding="utf-8"?>
<source>
    <publisher>Workloupe</publisher>
    <publisherurl>https://workloupe.com</publisherurl>"""

    for vacancy in vacancies:
        xml_output += f"""
    <job>
        <title><![CDATA[{vacancy["vacancy_title"]}]]></title>
        <date><![CDATA[{vacancy["vacancy_creation_date"]}]]></date>
        <referencenumber><![CDATA[{vacancy["vacancy_id"]}]]></referencenumber>
        <url><![CDATA[https://workloupe.com/apply/{vacancy["vacancy_id"]}?source=feed]]></url>
        <company><![CDATA[{employer_name}]]></company>
        <city><![CDATA[{vacancy["vacancy_city"]}]]></city>
        <country><![CDATA[{vacancy["vacancy_country"]}]]></country>
        <remote><![CDATA[{"true" if "remote" in str(vacancy["office_schedule"]).lower() else "false"}]]></remote>
        <description><![CDATA[{vacancy["vacancy_job_description"]}]]></description>
        <website><![CDATA[https://workloupe.com/employers/{employer["employer_id"]}]]></website>
        <schedule><![CDATA[{str(vacancy["office_schedule"]).lower()}]]></schedule>
        <email><![CDATA[<EMAIL>]]></email>
        <salarymin><![CDATA[{vacancy["salary_min"]}]]></salarymin>
        <salarymax><![CDATA[{vacancy["salary_max"]}]]></salarymax>
        <salarycurrency><![CDATA[{vacancy["salary_currency"]}]]></salarycurrency>
        <salary><![CDATA[{vacancy["salary_min"]} - {vacancy["salary_max"]} {vacancy["salary_currency"]}]]></salary>
    </job>"""

    xml_output += """
</source>"""

    return xml_output, 200, {"Content-Type": "application/xml"}


def clean_description(description):
    # Clean the description to be more readable and easier to parse.
    if not description:
        return ""

        # Clean HTML tags and entities and remove a tags to not have empty links on the raw description and remove emojis.
    description_without_markups = description

    # Remove Emojis
    description_without_markups = description_without_markups.encode(
        "ascii", "ignore"
    ).decode("ascii")

    # remove a tags and anything inside of them to not have empty links on the raw description.
    description_without_markups = re.sub(
        "<a[^>]*>(.*?)</a>", "", description_without_markups
    )

    # Add spacing for headers with attribute support
    description_without_markups = re.sub(
        "<h1[^>]*>(.*?)</h1>", "\n\n\\1\n\n", description_without_markups
    )
    description_without_markups = re.sub(
        "<h2[^>]*>(.*?)</h2>", "\n\n\\1\n\n", description_without_markups
    )
    description_without_markups = re.sub(
        "<h3[^>]*>(.*?)</h3>", "\n\\1\n", description_without_markups
    )

    # Add spacing for paragraphs with attribute support
    description_without_markups = re.sub(
        "<p[^>]*>(.*?)</p>", "\\1\n\n", description_without_markups
    )

    # Add spacing for lists with attribute support
    description_without_markups = re.sub(
        "<li[^>]*>(.*?)</li>", "\n * \\1", description_without_markups
    )
    description_without_markups = re.sub("<ul[^>]*>", "\n", description_without_markups)
    description_without_markups = re.sub("</ul>", "\n\n", description_without_markups)
    description_without_markups = re.sub("<ol[^>]*>", "\n", description_without_markups)
    description_without_markups = re.sub("</ol>", "\n\n", description_without_markups)

    # Replace HTML space entities with actual spaces
    description_without_markups = description_without_markups.replace("&nbsp;", " ")

    # Remove remaining HTML tags and entities
    description_without_markups = re.sub("<[^>]+>", "", description_without_markups)
    description_without_markups = re.sub("&lt;.*?&gt;", "", description_without_markups)
    description_without_markups = description_without_markups.strip()

    # Replace multiple spaces with single space and preserve line breaks
    description_clean_spaces = re.sub("[ \t]+", " ", description_without_markups)

    # Add proper line breaks for sections and preserve existing spacing
    clean_description = description_clean_spaces.replace(". ", ".\n\n").replace(
        "\n\n\n", "\n\n"
    )

    # Handle common section headers by ensuring they have space after them
    clean_description = re.sub(r"([A-Za-z]+):", r"\1: ", clean_description)

    # Ensure there's space between sentences that don't end with periods
    clean_description = re.sub(r"([a-z])([A-Z])", r"\1 \2", clean_description)

    # ensure that the first letter is capitalized
    clean_description = clean_description[0].upper() + clean_description[1:]

    # Ensure that the standard is UTF-8
    clean_description = clean_description.encode("utf-8", "ignore").decode("utf-8")

    return clean_description

def clean_html_description(description):
    # Keep the HTML tags but clean all the styling and the unnecessary attributes
    # for example <h1 style="color: red;">Hello</h1> becomes <h1>Hello</h1>
    clean_html = description

    # remove a tags and anything inside of them to not have empty links on the raw description.
    clean_html = re.sub(
        "<a[^>]*>(.*?)</a>", "", clean_html
    )

    # Remove
    clean_html = re.sub("&lt;.*?&gt;", "", clean_html)

    # Replace HTML space entities with actual spaces
    clean_html = clean_html.replace("&nbsp;", " ")

    # Remove style attributes
    clean_html = re.sub(r'\s+style="[^"]*"', '', clean_html)

    # Remove font tags and their attributes
    clean_html = re.sub(r'<font[^>]*>', '', clean_html)
    clean_html = re.sub(r'</font>', '', clean_html)

    # Remove span tags and their attributes
    clean_html = re.sub(r'<span[^>]*>', '', clean_html)
    clean_html = re.sub(r'</span>', '', clean_html)

    # Remove div attributes but keep tags
    clean_html = re.sub(r'<div[^>]*>', '<div>', clean_html)

    # Standardize paragraph tags
    clean_html = re.sub(r'<p[^>]*>', '<p>', clean_html)
    clean_html = re.sub(r'</p>', '</p>\n\n', clean_html)

    # Standardize list tags
    clean_html = re.sub(r'<li[^>]*>', '<li>', clean_html)
    clean_html = re.sub(r'</li>', '</li>\n', clean_html)
    clean_html = re.sub(r'<ul[^>]*>', '<ul>', clean_html)
    clean_html = re.sub(r'</ul>', '</ul>\n\n', clean_html)
    clean_html = re.sub(r'<ol[^>]*>', '<ol>', clean_html)
    clean_html = re.sub(r'</ol>', '</ol>\n\n', clean_html)

    # Standardize header tags
    clean_html = re.sub(r'<h1[^>]*>', '<h1>', clean_html)
    clean_html = re.sub(r'</h1>', '</h1>\n\n', clean_html)
    clean_html = re.sub(r'<h2[^>]*>', '<h2>', clean_html)
    clean_html = re.sub(r'</h2>', '</h2>\n\n', clean_html)
    clean_html = re.sub(r'<h3[^>]*>', '<h3>', clean_html)
    clean_html = re.sub(r'</h3>', '</h3>\n\n', clean_html)
    clean_html = re.sub(r'<h4[^>]*>', '<h4>', clean_html)
    clean_html = re.sub(r'</h4>', '</h4>\n\n', clean_html)
    clean_html = re.sub(r'<h5[^>]*>', '<h5>', clean_html)
    clean_html = re.sub(r'</h5>', '</h5>\n\n', clean_html)
    clean_html = re.sub(r'<h6[^>]*>', '<h6>', clean_html)
    clean_html = re.sub(r'</h6>', '</h6>\n\n', clean_html)

    # Remove any remaining style-related attributes
    clean_html = re.sub(r'\s+(class|id|style|size|color|background-color|max-height|overflow-y)="[^"]*"', '', clean_html)

    # Clean up extra whitespace
    clean_html = re.sub(r'\s+', ' ', clean_html)

    return clean_html.strip()



@app.route("/all/jobs.rss")
def xml_feed_all():
    # Get active vacancies for employer that have PostJobFree in job_portals and join with location details
    conn, cur = ats_safe_connect()

    cur.execute(
        """SELECT * FROM (SELECT v.vacancy_id, v.vacancy_title, v.vacancy_creation_date, v.vacancy_status, v.vacancy_job_description,
        v.salary_min, v.salary_max, v.salary_currency, v.vacancy_city, v.vacancy_country, e.employer_id, e.employer_name
        FROM feed_vacancy v
        INNER JOIN feed_employer e ON v.employer_id::text = e.employer_id::text
        WHERE v.vacancy_status = 'Active' AND v.job_portals LIKE '%PostJobFree%'
        ) x
        INNER JOIN
        (
        SELECT * FROM feed_officelocation
        ) y
        ON
        x.vacancy_city::text = y.city::text
        AND x.employer_id::text = y.employer_id::text
        --y.country::text = x.vacancy_country::text
        """,
    )
    vacancies = [
        dict(zip([col[0] for col in cur.description], row)) for row in cur.fetchall()
    ]

    cur.close()

    # Process each vacancy to add clean description and location
    for vacancy in vacancies:
        # Clean description
        vacancy["clean_description"] = clean_description(
            vacancy["vacancy_job_description"]
        )
        # Parse location details
        if isinstance(vacancy["location_details"], dict):
            location_details = vacancy["location_details"]
        else:
            location_details = json.loads(vacancy["location_details"])

        # Extract location information
        vacancy["country_code"] = pycountry.countries.get(name=location_details["country"]).alpha_2
        vacancy["state"] = location_details["state"]
        vacancy["zip_code"] = location_details["postcode"]
        vacancy["location_full"] = location_details["formatted"]
        vacancy["postcode"] = location_details["postcode"]

    # Create XML response
    xml_output = f"""<?xml version="1.0" encoding="utf-8"?>
<source>
    <publisher>Workloupe</publisher>
    <publisherurl>https://workloupe.com</publisherurl>
    <timezone>UTC</timezone>
    <lastBuildDate>{datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")}</lastBuildDate>"""

    for vacancy in vacancies:
        xml_output += f"""
    <job>
        <title>{vacancy["vacancy_title"]}</title>
        <date>{vacancy["vacancy_creation_date"]}</date>
        <referencenumber>Workloupe-{vacancy["vacancy_id"]}</referencenumber>
        <url>https://workloupe.com/apply/{vacancy["vacancy_id"]}?source=postjobfree</url>
        <company>{vacancy["employer_name"]}</company>


        <location>{vacancy["location_full"]}</location>

        <description>{vacancy["clean_description"]}</description>

        <email><EMAIL></email>
        <cpc>0.00</cpc>
        <salary>{"Unknown" if not vacancy["salary_max"] or float(vacancy["salary_max"]) <= 10 else f"Up to {vacancy['salary_max']} {vacancy['salary_currency']} per month"}</salary>
    </job>"""

    xml_output += """
</source>"""

    return xml_output, 200, {"Content-Type": "application/xml"}

@app.route("/all/himalayas.rss")
def xml_feed_himalayas():
    # Get active vacancies for employer that have Himalayas in job_portals and join with location details
    conn, cur = ats_safe_connect()

    # Get active vacancies with employer details
    cur.execute(
        """
        SELECT * FROM (SELECT v.*, e.employer_name, e.employer_logo_url, e.employer_description, e.employer_social_portals, e.employer_email, e.office_locations
        FROM feed_vacancy v
        INNER JOIN feed_employer e ON v.employer_id::text = e.employer_id::text
        WHERE v.vacancy_status = 'Active' AND v.job_portals LIKE '%Himalayas%' AND v.office_schedule = 'Fully Remote'
        ) x
        INNER JOIN
        (
        SELECT * FROM feed_officelocation
        ) y
        ON
        x.vacancy_city::text = y.city::text
        AND x.employer_id::text = y.employer_id::text
        --y.country::text = x.vacancy_country::text
        """
    )

    vacancies = [dict(zip([col[0] for col in cur.description], row)) for row in cur.fetchall()]

    cur.close()

    # Create XML feed
    xml_output = """<?xml version="1.0" encoding="utf-8"?>
<rss version="2.0">
    <source>
        <title>Workloupe.com Jobs Feed for Himalayas.app</title>
        <link>https://workloupe.com</link>
        <description>Remote job opportunities from Workloupe</description>

    <jobs>
    """

    for vacancy in vacancies:
        # Clean description
        clean_desc = clean_html_description(vacancy["vacancy_job_description"])

        # get location details
        if isinstance(vacancy["location_details"], dict):
            location_details = vacancy["location_details"]
        else:
            location_details = json.loads(vacancy["location_details"])


        # Format social links if available
        social_links = []
        if vacancy["employer_social_portals"]:
            for portal in str(vacancy["employer_social_portals"]).split(","):
                try:
                    platform, url = portal.strip().split(";")
                    social_links.append(f"<social platform='{platform}'><![CDATA[{url}]]></social>")
                except:
                    continue

        # Determine seniority level
        title_lower = vacancy["vacancy_title"].lower()
        if "senior" in title_lower or "sr." in title_lower:
            seniority = "Senior"
        elif any(level in title_lower for level in ["junior", "jr.", "entry", "intern"]):
            seniority = "Entry-Level"
        else:
            seniority = "Mid-Level"

        # Extract employer url from the employer mail
        company_url = vacancy["employer_email"].split("@")[1]

        # Office Location country unique
        office_locations = []
        for location in str(vacancy["office_locations"]).split("|"):
            try:
                country = location.split(",")[-1].strip()
                office_locations.append(country)
            except:
                continue

        xml_output += f"""
        <job>
            <job_title><![CDATA[{vacancy["vacancy_title"]}]]></job_title>
            <job_description><![CDATA[{clean_desc}]]></job_description>
            <country_requirements><![CDATA[{location_details["country"] or ""}]]></country_requirements>
            <application_link><![CDATA[https://workloupe.com/apply/{vacancy["vacancy_id"]}?source=himalayas]]></application_link>
            <company_name><![CDATA[{vacancy["employer_name"]}]]></company_name>
            <company_url><![CDATA[https://{company_url}]]></company_url>
            <expiration_date><![CDATA[{(vacancy["vacancy_creation_date"] + datetime.timedelta(days=60)).strftime("%a, %d %b %Y %H:%M:%S %z")}]]></expiration_date>
            <publication_date><![CDATA[{vacancy["vacancy_creation_date"].strftime("%a, %d %b %Y %H:%M:%S %z") if vacancy["vacancy_creation_date"] else ""}]]></publication_date>
            <seniority><![CDATA[{seniority}]]></seniority>
            <employment_type><![CDATA[{vacancy["work_schedule"] or "full-time"}]]></employment_type>
            <min_salary><![CDATA[{vacancy["salary_min"] or ""}]]></min_salary>
            <max_salary><![CDATA[{vacancy["salary_max"] or ""}]]></max_salary>
            <currency><![CDATA[{vacancy["salary_currency"] or ""}]]></currency>
            <company_about><![CDATA[{vacancy["employer_description"] or ""}]]></company_about>
            <company_logo><![CDATA[{vacancy["employer_logo_url"] or ""}]]></company_logo>
            <company_countries><![CDATA[{",".join(office_locations)}]]></company_countries>
            {"".join(social_links)}
        </job>"""

    xml_output += """
    </jobs>
    </source>
</rss>"""

    cur.close()
    return xml_output, 200, {"Content-Type": "application/xml"}


if __name__ == "__main__":
    app.run(host="0.0.0.0", debug=True, port=8888)
