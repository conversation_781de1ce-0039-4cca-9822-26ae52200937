:root {
    /* Primary Theme Colors */
    --bs-primary: #8A4962; 
    /* Primary color */
    --bs-secondary: #3D1D5A;
    /* Secondary color */
    --bs-success: #1C501C;
    /* Success color */

    /* Light Variants */
    --bs-primary-bg-subtle: #FAD2E1;
    /* Light primary background */
    --bs-secondary-bg-subtle: #DBC5F0;
    /* Light secondary background */
    --bs-success-bg-subtle: #C5DDBE;
    /* Light success background */

    /* Dark Variants */
    --bs-primary-bg-dark: #6A2C3A;
    --bs-secondary-bg-dark: #2A0F2A;
    --bs-success-bg-dark: #0D3D0D;

    /* Very Light Variants */
    --bs-primary-bg: #F8E4EC;
    /* Very light primary background */
    --bs-secondary-bg: #ECE1F6;
    /* Very light secondary background */
    --bs-success-bg: #DFEBDC;
    /* Very light success background */

    /* Additional Colors */
    --bs-body-bg: #fcfcfc;
    /* Custom white background */
    --bs-light-gray: #f8f9fa;

}


body, html {
    overflow-x: clip !important;
    width: 100vw !important;
    font-family: 'Montserrat' !important;
}

.limited-width-content {
    max-width: 1200px !important;
    margin: auto !important;
}


.less-limited-width-content {
    max-width: 1600px !important;
    margin: auto !important;
}

.job-icon {
    max-width: 40px;
    max-height: 40px;
}

.big-button {
    height: 100px;
}

.space-from-top {
    margin-top: 10vw !important;
}

.space-from-bottom {
    margin-bottom: 6vh !important;
}

.space-from-bottom-xl {
    margin-bottom: 10vw !important;
}


.stretch-text {
    text-align: justify;
    letter-spacing: 3px;
}

.footer-links {
    font-size: 14px !important;
    margin-top: 4px;
    margin-bottom: 4px;
    display: block;
    color: #7a7a7a;
    text-decoration: none;
}


.learn-links {
    font-size: 14px !important;
    margin-top: 4px;
    margin-bottom: 4px;
    display: block;
    color: #0e0e0e;
    text-decoration: none;
}


.main-link-item {
    text-decoration: none;
    padding-bottom: 8px !important;
    color: #000 !important;
    font-size: 24px !important;
    display: block;
}

#footer {
    background-color: #f6f6f6;
}

.carousel-indicators {
    list-style-type: none;
}

.carousel-item {
    overflow: hidden;
    width: 100%;
}

#demo-req-form {
    max-width: 600px !important;
}

#demo-request-text {
    padding-top: 12vh !important;
    padding-bottom: 8vh !important;
}


.pricing-card:hover {
    box-shadow: 0px 0px 10px #6a6a6a !important;
}

.feature-card {
    border-radius: 8px;
    border-style: solid;
    border-color: #FFF;
    border-width: 2px;
    background-color: #fff;
    column-fill: auto;
    position: relative;
    box-shadow: 0px 0px 15px #7a7a7a !important;
}

#transition-from-red {
    background: url(./Static/Illustrations/arkaplan_2.svg);
    background-size: cover;
    background-position: center bottom;
}

#transition-to-red {
    background: url(./Static/Illustrations/arkaplan.svg);
    background-size: cover;
}

#transition-to-gray {
    background: url(./Static/Illustrations/arkaplan_3.svg);
    background-size: cover;
}

#transition-from-gray {
    background: url(./Static/Illustrations/arkaplan_3.svg);
    background-size: cover;
    transform: scaleY(-1);
}


.lrn-more-btn {
    border-top-left-radius: 0%;
    border-top-right-radius: 0%;
    position: absolute;
    bottom: 0;
    width: 100%;
}


.feature-info-card {
    background-color: #ffffff;
    border-radius: 24px;
    padding: 10px;
    margin: 10px;
    margin-bottom: 30px;
}

.landing-info-card {
    background-color: #f8eeee;
    border-radius: 24px;
    padding: 10px;
    margin: 10px;
    margin-bottom: 30px;
}

.landing-info-card-alt {
    background-color: #F0F0F0;
    border-radius: 24px;
    padding: 10px;
    margin: 10px;
    margin-bottom: 30px;
}

.feature-info-card-text {
    max-width: 450px;
    color: #000;
}

.demo-img {
    max-height: 300px;
}

.client-logo {
    max-height: 80px !important;
    max-width: 120px !important;
    width: 100%;
}

#employer-message {
    margin-top: 5%;
    margin-bottom: 5%;
    margin-left: 2%;
    margin-right: 2%;
}

.employer-banner {
    background-size: cover;
    background-position: center;
    height: 20vw;
    width: auto;
    margin-bottom: -4vw;
}

.employer-icon {
    height: 100%;
    width: 100%;
    max-height: 200px;
    max-width: 200px;
    min-width: 100px;
    min-height: 100px;
}

.employer-icon-sm {
    min-height: 40px;
    min-width: 40px;
}


.employer-icon-md {
    min-height: 60px;
    min-width: 60px;
}


.employer-icon-card {
    min-height: 40px;
    min-width: 40px;
    max-height: 100px;
    max-width: 100px;
}

.gallery-item {
    height: 100%;
    width: 100%;
    max-height: 200px;
    min-width: 100px;
    min-height: 100px;
    filter: blur(3px) grayscale(0.7);
    object-fit: cover;
}

.gallery-button {
    height: 100px;
    border: #000;
}

.floating-btn {
    margin-top: -66px;
    margin-bottom: -66px;
    z-index: 99;
}

.listing {
    opacity: 0;
    visibility: hidden;
    height: 0px;
    transition: opacity 0.5s ease, visibility 0.5s ease, height 0s ease;
    background-color: white;
}

.listing * {
    max-height: 100% !important;
    overflow-y: hidden;
}

.listing.visible {
    opacity: 1;
    /* Fade in */
    height: fit-content;
    visibility: visible;
    /* Make it interactive */
}
.listing-pointer {
    background-color: transparent;
    transition: background-color 0.5s ease, border 0.5s ease;
    border-right: 2px solid transparent;
}

.listing-pointer.active {
    background-color: var(--bs-light-gray) !important;
    border-right: var(--bs-primary) 2px solid;
}

.sm-text {
    font-size: smaller;
}

.xs-text {
    font-size: x-small;
}

.mini-jobs {
    background-color: #ffffff;
    border-radius: 8px;
    transition: 0.1s ease;
    border: 1px solid #ffffff;
    max-width: 600px;
    margin-top: 4px;
    margin-bottom: 6px;
}

.mini-jobs:hover {
    border: 1px solid #b41e1e;

}


@media (min-width: 560px) {
    .mini-jobs {
        width: 80%;
    }
}


@media (min-width: 780px) {
    .mini-jobs {
        width: 70%;
    }
}


@media (min-width: 1000px) {
    .mini-jobs {
        width: 60%;
    }
}

.gradient-bg {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.job-card {
    transition: transform 0.2s ease-in-out;
    border-left: 4px solid var(--bs-primary-bg-subtle);
}

.job-card:hover {
    transform: translateY(-3px);
}

.company-logo {
    max-height: 50px;
    width: auto;
    object-fit: contain;
}

.salary-highlight {
    color: #1C501C;
    font-weight: 600;
}

.badge-remote {
    background-color: #e3f2fd;
    color: #0d6efd;
}

.section-heading {
    font-size: 2rem;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 0.5rem;
    margin-bottom: 2rem;
}

.employer-card {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.2s ease;
}


@media (max-width: 767px) {
    .hero-img {
        max-width: 300px;
        margin: auto;
    }
}

.employer-card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}


/* Altering the defaults of the bootstrap */

.text-primary {
    color: var(--bs-primary) !important;
}
a.text-primary:active, a.text-primary:hover {
    color: var(--bs-primary-bg-dark) !important;
}

.color-primary {
    color: var(--bs-primary) !important;
}

.bg-light-1 {
    background-color: var(--bs-primary-bg) !important;
}

.bg-light-2 {
    background-color: var(--bs-secondary-bg) !important;
}

.btn-primary {
    background-color: var(--bs-primary) !important;
    border-color: var(--bs-primary) !important;
}

.btn-primary:focus, .btn-primary:active, .btn-primary:hover {
    outline: none !important;
    box-shadow: none;
    background-color: var(--bs-primary-bg-dark) !important;
}

.btn-secondary {
    background-color: var(--bs-secondary) !important;
    border-color: var(--bs-secondary) !important;
}

.btn-secondary:focus, .btn-secondary:active, .btn-secondary:hover {
    outline: none !important;
    box-shadow: none;
    background-color: var(--bs-secondary-bg-dark) !important;
}

.btn-outline-primary {
    color: var(--bs-primary) !important;
    border-color: var(--bs-primary) !important;
}

.btn-outline-primary:focus, .btn-outline-primary:active, .btn-outline-primary:hover {
    outline: none !important;
    box-shadow: none;
    background-color: var(--bs-primary-bg-dark) !important;
    color: white !important;
}

.border-primary {
    border-color: var(--bs-primary) !important;
}


/* CSS Additions/Modifications */
#search-div {
    background: #fff;
    border: 1px solid #e0e0e0;
    transition: all 0.2s ease;
}

#search-div:focus-within {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(138, 73, 98, 0.15);
}

/* Enhanced Form Controls */
.form-control.border-2 {
    border-width: 2px !important;
    transition: all 0.3s ease;
}

.form-control.border-2:focus {
    border-color: var(--bs-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(138, 73, 98, 0.15) !important;
}

/* Loading Indicator */
.htmx-indicator {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.htmx-request .htmx-indicator {
    opacity: 1;
}

.htmx-request.htmx-indicator {
    opacity: 1;
}

/* Location Autocomplete Dropdown */
.location-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    max-height: 200px;
    overflow-y: auto;
}

.location-suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.location-suggestion-item:hover {
    background-color: var(--bs-primary-bg-subtle);
}

.location-suggestion-item:last-child {
    border-bottom: none;
}

/* Modern Card Styles */
.modern-card {
    background: white;
    border: 1px solid #e8ecef;
    transition: all 0.3s ease;
    overflow: hidden;
}

.modern-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Job Card Mini Styles */
.job-card-mini {
    background: white;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.job-card-mini:hover {
    border-color: var(--bs-primary-bg-subtle);
    background: var(--bs-primary-bg);
}

.job-card-mini.active {
    border-color: var(--bs-primary);
    background: var(--bs-primary-bg-subtle);
    box-shadow: 0 4px 12px rgba(138, 73, 98, 0.15);
}

/* Job Description Content */
.job-description-content {
    line-height: 1.7;
}

.job-description-content h1,
.job-description-content h2,
.job-description-content h3,
.job-description-content h4,
.job-description-content h5,
.job-description-content h6 {
    color: var(--bs-primary);
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.job-description-content ul,
.job-description-content ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
}

.job-description-content li {
    margin-bottom: 0.5rem;
}

/* Job Sidebar */
.job-sidebar {
    border: 1px solid #e8ecef;
}

.fact-item {
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f0f0f0;
}

.fact-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.employer-card {
    background: white;
    border: 1px solid #eee;
    border-radius: 8px;
    transition: box-shadow 0.2s ease;
    overflow: hidden;
}

.employer-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.company-logo-wrapper {
    padding: 1rem;
    border-bottom: 1px solid #f5f5f5;
    background: #fafafa;
}

.employer-icon-md {
    height: 60px;
    width: 60px;
    object-fit: contain;
    padding: 4px;
    background: white;
    border-radius: 6px;
    border: 1px solid #eee;
}

.company-details {
    padding: 1rem;
    font-size: 0.9rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f5f5f5;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    color: #666;
    font-weight: 500;
}

.detail-value {
    color: #333;
    text-align: right;
    max-width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.open-positions {
    background: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Add these CSS styles */
/* Company Detail Page Styles */
.company-header {
    background-position: center;
    background-size: cover;
    height: 200px;
    position: relative;
}

.company-info-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    position: relative;
    margin-top: -60px;
    padding-top: 80px;
}

.company-logo-card {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    border-radius: 8px;
    background: white;
    padding: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #eee;
}

.detail-section {
    border-bottom: 1px solid #eee;
    padding: 2rem 0;
}

.detail-section:last-child {
    border-bottom: none;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.contact-method i {
    font-size: 1.25rem;
    color: var(--bs-primary);
}

.contact-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--bs-primary);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.gallery-item {
    aspect-ratio: 1;
    object-fit: cover;
    border-radius: 8px;
    transition: transform 0.2s ease;
}

.gallery-item:hover {
    transform: scale(1.03);
}

.job-listing {
    padding: 1.5rem;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 1rem;
    transition: border-color 0.2s ease;
}

.job-listing:hover {
    border-color: var(--bs-primary);
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 6px;
}

.social-link:hover {
    background: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
}

/* Gallery Modal Adjustments */
.modal-gallery .carousel-item {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    max-height: 70vh;
}

.modal-gallery img {
    object-fit: contain;
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
}

/* Existing Styles from Previous Versions */
.limited-width-content {
    max-width: 1200px !important;
    margin: auto !important;
}

.employer-card {
    background: white;
    border: 1px solid #eee;
    border-radius: 8px;
    transition: box-shadow 0.2s ease;
    overflow: hidden;
}

.employer-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}