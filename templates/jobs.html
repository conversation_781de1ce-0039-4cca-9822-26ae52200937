{% extends "base.html" %} {% block title %}Vacancies{% endblock %} {% block
content %}
<div class="less-limited-width-content">
  <div
    id="search-div"
    class="modern-card rounded-3 p-4 mb-4 shadow-sm"
  >
    <div class="text-center mb-4">
      <h2 class="h4 fw-bold text-primary mb-2">
        <i class="bi bi-search me-2"></i>Find Your Dream Job
      </h2>
      <p class="text-muted mb-0">Search through thousands of opportunities</p>
    </div>

    <div class="row g-3">
      <div class="col-12 col-md-6">
        <div class="form-floating">
          <input
            class="form-control border-2"
            id="keyword-input"
            name="keyword-input"
            placeholder=""
            hx-get="/filterjobs"
            hx-target="#results-div"
            hx-trigger="keyup changed delay:300ms"
            hx-include="#location-input"
            hx-indicator="#search-loading"
          />
          <label for="keyword-input">
            <i class="bi bi-briefcase me-2"></i>Job Title, Skills, or Keywords
          </label>
        </div>
      </div>
      <div class="col-12 col-md-6">
        <div class="form-floating position-relative">
          <input
            class="form-control border-2"
            id="location-input"
            name="location-input"
            placeholder=""
            autocomplete="off"
            hx-get="/filterjobs"
            hx-target="#results-div"
            hx-trigger="keyup changed delay:300ms"
            hx-include="#keyword-input"
            hx-indicator="#search-loading"
          />
          <label for="location-input">
            <i class="bi bi-geo-alt me-2"></i>Location (City, Country, or Remote)
          </label>
          <div id="location-suggestions" class="location-dropdown"></div>
        </div>
      </div>
    </div>

    <div class="text-center mt-3">
      <div id="search-loading" class="htmx-indicator">
        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <span class="text-muted">Searching jobs...</span>
      </div>
    </div>
  </div>

  <div id="results-div" class="mt-4 row">
    <!-- Jobs List Column -->
    <div class="col-12 col-md-4 col-lg-3 mb-4 mb-md-0">
      <div
        id="jobs-small-list"
        class="rounded py-4 px-2 row shadow-sm mx-0"
        style="background-color: white"
      >
        <h5 class="text-start px-3"><strong>Related Jobs</strong></h5>
        <hr />
        <div
          id="sorted-jobs-short"
          class="col-12 overflow-auto"
          style="max-height: 60vh; height: max-content"
        >
          <div id="jobs-listed">
            {% for vacancy in all_vacancies %}
            <div class="listing-item">
              <div
                id="button-{{vacancy.vacancy_id}}"
                class="row g-2 my-auto listing-pointer {% if loop.first %} active {% endif %}"
                onclick="switch_listing('button-{{vacancy.vacancy_id}}','listing-div-{{vacancy.vacancy_id}}')"
              >
                <div class="col-3 col-md-2">
                  <img
                    src="{{vacancy.employer_logo_url}}"
                    class="img-fluid rounded employer-icon-sm"
                    alt="Job Icon"
                    style="max-width: 40px"
                  />
                </div>
                <div class="col-9 col-md-10">
                  <h6 class="mb-1">{{vacancy.vacancy_title}}</h6>
                  <p class="mb-0">
                    <small class="text-muted">
                      {{vacancy.employer_name}}<br />
                      {% if vacancy.vacancy_city %} {{vacancy.vacancy_city}}, {% endif %} {{vacancy.vacancy_country}}
                    </small>
                  </p>
                </div>
              </div>
            </div>
            <hr class="my-2" />
            {% endfor %}
          </div>
        </div>
      </div>
    </div>

    <!-- Job Details Column -->
    <div class="col-12 col-md-8 col-lg-9">
      {% for vacancy in all_vacancies %}
      <div
        id="listing-div-{{vacancy.vacancy_id}}"
        class="rounded listing shadow-sm {% if loop.first %} visible {% endif %}"
      >
        <div
          class="rounded-top w-100"
          style="
            height: 120px;
            background-image: url('{{vacancy.employer_banner_url}}');
            background-size: cover;
            background-position: center;
            {% if vacancy.listing_source == 'WeWorkRemotely' %}
            background-image: radial-gradient(circle at 50% 50%, #353535, #2a2a2a, #1f1f1f, #141414, #0f0f0f, #080808, #000000);
            {% endif %}
          "
        ></div>

        <div class="row g-3 m-2 m-md-4">
          <div class="col-12 col-lg-8 border-lg-end mb-4 mb-lg-0">
            <div class="row align-items-center mb-4">
              <div class="col-4 col-sm-3 col-md-2 p-2">
                <img
                  src="{{vacancy.employer_logo_url}}"
                  class="img-fluid rounded employer-icon-md"
                  alt="Job Icon"
                  style="max-width: 80px"
                />
              </div>

              <div class="col-8 col-sm-9 col-md-10">
                <h3 class="h4 h2-md">{{vacancy.vacancy_title}}</h3>
                <p class="mb-0">
                  <small class="text-muted"> {{vacancy.employer_name}} </small>
                </p>
              </div>
            </div>

            <hr class="my-2 d-none d-md-block" />

            <div name="job-desc" class="p-2">
              {{ vacancy.vacancy_job_description|safe }}
            </div>
          </div>

          <div class="col-12 col-lg-4 ps-lg-4">
            <div name="quick-facts" class="mb-4">
              <h6 class="h5">Position Facts</h6>
              <div class="mt-3">
                <p class="mb-3">
                  <strong>Monthly Salary Budget:</strong><br />
                  {{vacancy.salary_min}} - {{vacancy.salary_max}}
                  {{vacancy.salary_currency}}
                </p>
                <p class="mb-3">
                  <strong>Work Mode:</strong><br />
                  {{vacancy.office_schedule}}
                </p>
                <p class="mb-3">
                  <strong>Work Location:</strong><br />
                  {% if vacancy.vacancy_city %}
                  {{vacancy.vacancy_city}}, {% endif %} {{vacancy.vacancy_country}}
                </p>
                
              </div>
            </div>

            <hr class="my-2" />

            {% if all_tags[vacancy.vacancy_id][0] %}
            <div name="highlight-badges" class="my-4">
              <h6 class="h5">Key Advantages</h6>
              <div class="d-flex flex-wrap gap-2 mt-3">
                {% for tag in all_tags[vacancy.vacancy_id] %}
                <span class="badge bg-dark text-wrap p-2 m-0">
                  <small>{{tag}}</small>
                </span>
                {% endfor %}
              </div>
            </div>


            <hr class="my-2" />
            {% endif %}


            <div name="apply-now" class="mt-4">
              <div class="d-grid gap-2">
                {% if vacancy.listing_source == "Canvider" %}
                <a
                  href="{{url_for('view_vacancy',vacancy_id=vacancy.vacancy_id)}}"
                  target="_blank"
                  class="btn btn-outline-dark btn-lg my-1 shadow-sm"
                >
                  Apply Now <i class="bi bi-arrow-up-right-square-fill"></i>
                </a>
                {% else %}
                <a
                  href="{{vacancy.vacancy_url}}"
                  target="_blank"
                  class="btn btn-outline-dark btn-lg my-1 shadow-sm"
                >
                  Apply Now <i class="bi bi-arrow-up-right-square-fill"></i>
                </a>
                {% endif %}

                {% if vacancy.listing_source == "Canvider" %}
                <a
                  href="{{url_for('employer',employer_id=vacancy.employer_id)}}"
                  target="_blank"
                  class="btn btn-outline-dark btn-lg mt-1 mb-3 shadow-sm"
                >
                  Company Page <i class="bi bi-arrow-up-right-square-fill"></i>
                </a>
                {% else %}
                <a
                  href="{{vacancy.company_url}}"
                  target="_blank"
                  class="btn btn-outline-dark btn-lg mt-1 mb-3 shadow-sm"
                >
                  Company Page <i class="bi bi-arrow-up-right-square-fill"></i>
                </a>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</div>

<style>
  @media (max-width: 768px) {
    #jobs-small-list {
      margin: 0 0.5rem;
    }

    .listing-item {
      padding: 0.5rem;
    }

    .employer-icon-sm {
      max-width: 30px;
    }

    #search-div {
      margin: 0 0.5rem;
    }

    .listing {
      margin-top: 1rem;
    }

    .border-lg-end {
      border-right: none !important;
    }
  }

  @media (max-width: 576px) {
    .employer-icon-md {
      max-width: 60px;
    }

    h3 {
      font-size: 1.25rem;
    }

    .h5 {
      font-size: 1.1rem;
    }

    .badge {
      font-size: 0.8rem;
    }
  }
</style>

<script>
  // Location autocomplete functionality
  document.addEventListener('DOMContentLoaded', function() {
    const locationInput = document.getElementById('location-input');
    const suggestionsDiv = document.getElementById('location-suggestions');
    let debounceTimer;

    locationInput.addEventListener('input', function() {
      clearTimeout(debounceTimer);
      const query = this.value.trim();

      if (query.length < 2) {
        suggestionsDiv.style.display = 'none';
        return;
      }

      debounceTimer = setTimeout(() => {
        fetch(`/search-locations?q=${encodeURIComponent(query)}`)
          .then(response => response.json())
          .then(data => {
            if (data.locations && data.locations.length > 0) {
              showSuggestions(data.locations);
            } else {
              suggestionsDiv.style.display = 'none';
            }
          })
          .catch(error => {
            console.error('Error fetching locations:', error);
            suggestionsDiv.style.display = 'none';
          });
      }, 200);
    });

    function showSuggestions(locations) {
      suggestionsDiv.innerHTML = '';
      locations.forEach(location => {
        const item = document.createElement('div');
        item.className = 'location-suggestion-item';
        item.textContent = location;
        item.addEventListener('click', function() {
          locationInput.value = location;
          suggestionsDiv.style.display = 'none';
          // Trigger the search
          htmx.trigger(locationInput, 'keyup');
        });
        suggestionsDiv.appendChild(item);
      });
      suggestionsDiv.style.display = 'block';
    }

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(event) {
      if (!locationInput.contains(event.target) && !suggestionsDiv.contains(event.target)) {
        suggestionsDiv.style.display = 'none';
      }
    });
  });

  function switch_listing(btnId, targetId) {
    document.querySelectorAll(".listing").forEach((content) => {
      content.classList.remove("visible");
    });

    const targetDiv = document.getElementById(targetId);
    if (targetDiv) targetDiv.classList.add("visible");

    document.querySelectorAll(".listing-pointer").forEach((content) => {
      content.classList.remove("active");
    });

    const sourceDiv = document.getElementById(btnId);
    if (sourceDiv) sourceDiv.classList.add("active");

    // Scroll to top of listing on mobile
    if (window.innerWidth < 768) {
      targetDiv.scrollIntoView({ behavior: "smooth" });
    }
  }
</script>

{% endblock %}
