{% extends "base.html" %} {% block title %}Employers{% endblock %} {% block
content %}
<div class="less-limited-width-content px-3">
  <div class="container-fluid py-4">
    <!-- Modern Header -->
    <div class="text-center mb-5">
      <h1 class="display-4 fw-bold mb-3">
        <i class="bi bi-building text-primary me-3"></i>
        Explore Top Employers
      </h1>
      <p class="lead text-muted mb-4">
        Discover innovative organizations shaping the future of work
      </p>
      <div class="d-flex justify-content-center">
        <span class="badge bg-primary bg-opacity-10 text-primary px-4 py-2 fs-6">
          <i class="bi bi-briefcase me-2"></i>{{all_employers|length}} Companies Available
        </span>
      </div>
    </div>

    <!-- Modern Search Interface -->
    <div class="modern-card rounded-3 p-4 mb-5 shadow-sm">
      <div class="row align-items-center">
        <div class="col-md-8 offset-md-2">
          <div class="form-floating">
            <input
              class="form-control border-2"
              id="keyword-input"
              name="keyword-input"
              placeholder=""
              hx-get="/filteremp"
              hx-target="#employers-list"
              hx-trigger="keyup changed delay:300ms"
              hx-indicator="#search-loading"
            />
            <label for="keyword-input">
              <i class="bi bi-search me-2"></i>Search companies, industries, or locations...
            </label>
          </div>
          <div class="text-center mt-3">
            <div id="search-loading" class="htmx-indicator">
              <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <span class="text-muted">Searching companies...</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="employers-list" class="row g-3">
      {% for employer in all_employers %}
      <div class="col-xl-4 col-lg-6 col-md-6 col-12">
        <div class="employer-card">
          <div class="company-logo-wrapper">
            <div class="d-flex justify-content-between align-items-center">
              <img
                src="{{ employer.employer_logo_url }}"
                class="employer-icon-md"
                alt="{{ employer.employer_name }}"
              />
              <div class="open-positions">
                {{ employer.open_positions }} Openings
              </div>
            </div>
          </div>

          <div class="company-details">
            <h6 class="fw-bold mb-3">{{ employer.employer_name }}</h6>

            <div class="detail-item">
              <span class="detail-label">Industry</span>
              <span class="detail-value">{{ employer.employer_industry }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Employees</span>
              <span class="detail-value">{{ employer.employer_headcount }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Location</span>
              <span class="detail-value">{{ employer.headquarter }}</span>
            </div>
          </div>

          <a
            href="{{url_for('employer',employer_id=employer.employer_id)}}"
            class="btn btn-outline-primary w-100 py-2 border-top rounded-0"
          >
            View Details →
          </a>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</div>
{% endblock %}
