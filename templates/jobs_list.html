<!-- Jobs List Column -->
<div class="col-12 col-md-4 col-lg-3 mb-4 mb-md-0">
  <div
    id="jobs-small-list"
    class="modern-card rounded-3 py-4 px-3 shadow-sm mx-0"
  >
    <div class="d-flex align-items-center justify-content-between mb-3">
      <h5 class="mb-0 fw-bold text-primary">
        <i class="bi bi-briefcase me-2"></i>Job Listings
      </h5>
      <span class="badge bg-primary rounded-pill">{{all_vacancies|length}}</span>
    </div>
    <hr class="my-3" />
    <div
      id="sorted-jobs-short"
      class="overflow-auto"
      style="max-height: 65vh;"
    >
      <div id="jobs-listed">
        {% if all_vacancies %}
        {% for vacancy in all_vacancies %}
        <div class="listing-item mb-3">
          <div
            id="button-{{vacancy.vacancy_id}}"
            class="job-card-mini p-3 rounded-3 listing-pointer {% if loop.first %} active {% endif %}"
            onclick="switch_listing('button-{{vacancy.vacancy_id}}','listing-div-{{vacancy.vacancy_id}}')"
          >
            <div class="d-flex align-items-start">
              <div class="flex-shrink-0 me-3">
                <img
                  src="{{vacancy.employer_logo_url}}"
                  class="rounded-2 employer-icon-sm"
                  alt="{{vacancy.employer_name}} logo"
                  style="width: 48px; height: 48px; object-fit: cover;"
                  onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiByeD0iOCIgZmlsbD0iIzZjNzU3ZCIvPgo8dGV4dCB4PSIyNCIgeT0iMzAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPnt7dmFjYW5jeS5lbXBsb3llcl9uYW1lWzBdfX08L3RleHQ+Cjwvc3ZnPgo='"
                />
              </div>
              <div class="flex-grow-1 min-w-0">
                <h6 class="mb-1 fw-semibold text-truncate">{{vacancy.vacancy_title}}</h6>
                <p class="mb-1 text-muted small fw-medium">{{vacancy.employer_name}}</p>
                <div class="d-flex align-items-center text-muted small">
                  <i class="bi bi-geo-alt me-1"></i>
                  <span class="text-truncate">
                    {% if vacancy.vacancy_city %}{{vacancy.vacancy_city}}, {% endif %}{{vacancy.vacancy_country}}
                  </span>
                </div>
                {% if vacancy.listing_source == 'WeWorkRemotely' %}
                <span class="badge bg-success bg-opacity-10 text-success small mt-1">
                  <i class="bi bi-rss me-1"></i>Remote
                </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
        {% endfor %}
        {% else %}
        <div class="text-center py-5">
          <i class="bi bi-search display-4 text-muted mb-3"></i>
          <h5 class="text-muted">No jobs found</h5>
          <p class="text-muted small">Try adjusting your search criteria</p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Job Details Column -->
<div class="col-12 col-md-8 col-lg-9">
  {% if all_vacancies %}
  {% for vacancy in all_vacancies %}
  <div
    id="listing-div-{{vacancy.vacancy_id}}"
    class="modern-card rounded-3 listing shadow-sm {% if loop.first %} visible {% endif %}"
  >
    <!-- Modern Header with Gradient -->
    <div
      class="rounded-top-3 w-100 position-relative"
      style="
        height: 140px;
        background: {% if vacancy.listing_source == 'WeWorkRemotely' %}
          linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        {% else %}
          linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
        {% endif %}
        {% if vacancy.employer_banner_url %}
          background-image: url('{{vacancy.employer_banner_url}}');
          background-size: cover;
          background-position: center;
          background-blend-mode: overlay;
        {% endif %}
      "
    >
      <div class="position-absolute top-0 end-0 p-3">
        {% if vacancy.listing_source == 'WeWorkRemotely' %}
        <span class="badge bg-light text-dark">
          <i class="bi bi-rss me-1"></i>Remote Job
        </span>
        {% else %}
        <span class="badge bg-light text-dark">
          <i class="bi bi-building me-1"></i>Direct
        </span>
        {% endif %}
      </div>
    </div>

    <div class="p-4">
      <!-- Modern Job Header -->
      <div class="d-flex align-items-start mb-4">
        <div class="flex-shrink-0 me-4">
          <img
            src="{{vacancy.employer_logo_url}}"
            class="rounded-3 shadow-sm"
            alt="{{vacancy.employer_name}} logo"
            style="width: 80px; height: 80px; object-fit: cover;"
            onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiByeD0iMTIiIGZpbGw9IiM2Yzc1N2QiLz4KPHRleHQgeD0iNDAiIHk9IjUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMzAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj57e3ZhY2FuY3kuZW1wbG95ZXJfbmFtZVswXX19PC90ZXh0Pgo8L3N2Zz4K'"
          />
        </div>
        <div class="flex-grow-1">
          <h2 class="h3 fw-bold mb-2 text-dark">{{vacancy.vacancy_title}}</h2>
          <div class="d-flex align-items-center mb-2">
            <i class="bi bi-building text-primary me-2"></i>
            <span class="fw-semibold text-muted">{{vacancy.employer_name}}</span>
          </div>
          <div class="d-flex align-items-center text-muted">
            <i class="bi bi-geo-alt text-primary me-2"></i>
            <span>
              {% if vacancy.vacancy_city %}{{vacancy.vacancy_city}}, {% endif %}{{vacancy.vacancy_country}}
            </span>
          </div>
        </div>
      </div>

      <div class="row g-4">
        <!-- Job Description Section -->
        <div class="col-12 col-lg-8">
          <div class="job-description-content">
            {{ vacancy.vacancy_job_description|safe }}
          </div>
        </div>

        <!-- Modern Job Details Sidebar -->
        <div class="col-12 col-lg-4">
          <div class="job-sidebar bg-light rounded-3 p-4">
            <!-- Position Facts -->
            <div class="mb-4">
              <h6 class="fw-bold text-primary mb-3">
                <i class="bi bi-info-circle me-2"></i>Position Details
              </h6>
              <div class="fact-item mb-3">
                <div class="d-flex align-items-center mb-1">
                  <i class="bi bi-currency-dollar text-success me-2"></i>
                  <span class="small fw-semibold text-muted">Salary Range</span>
                </div>
                <div class="ms-4">
                  {% if vacancy.salary_min and vacancy.salary_max %}
                    {{vacancy.salary_min}} - {{vacancy.salary_max}} {{vacancy.salary_currency}}
                  {% else %}
                    Not specified
                  {% endif %}
                </div>
              </div>

              <div class="fact-item mb-3">
                <div class="d-flex align-items-center mb-1">
                  <i class="bi bi-laptop text-info me-2"></i>
                  <span class="small fw-semibold text-muted">Work Mode</span>
                </div>
                <div class="ms-4">
                  {% if vacancy.office_schedule %}
                    {{vacancy.office_schedule}}
                  {% else %}
                    Not specified
                  {% endif %}
                </div>
              </div>

              <div class="fact-item">
                <div class="d-flex align-items-center mb-1">
                  <i class="bi bi-geo-alt text-warning me-2"></i>
                  <span class="small fw-semibold text-muted">Location</span>
                </div>
                <div class="ms-4">
                  {% if vacancy.vacancy_city %}{{vacancy.vacancy_city}}, {% endif %}{{vacancy.vacancy_country}}
                </div>
              </div>
            </div>

            {% if all_tags.get(vacancy.vacancy_id) and all_tags[vacancy.vacancy_id][0] %}
            <!-- Key Advantages -->
            <div class="mb-4">
              <h6 class="fw-bold text-primary mb-3">
                <i class="bi bi-star me-2"></i>Key Benefits
              </h6>
              <div class="d-flex flex-wrap gap-2">
                {% for tag in all_tags[vacancy.vacancy_id] %}
                <span class="badge bg-primary bg-opacity-10 text-primary border border-primary border-opacity-25 px-3 py-2">
                  {{tag}}
                </span>
                {% endfor %}
              </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="d-grid gap-3">
              {% if vacancy.listing_source == "Canvider" %}
              <a
                href="{{url_for('view_vacancy',vacancy_id=vacancy.vacancy_id)}}"
                target="_blank"
                class="btn btn-primary btn-lg shadow-sm"
              >
                <i class="bi bi-send me-2"></i>Apply Now
              </a>
              <a
                href="{{url_for('employer',employer_id=vacancy.employer_id)}}"
                target="_blank"
                class="btn btn-outline-primary"
              >
                <i class="bi bi-building me-2"></i>View Company
              </a>
              {% else %}
              <a
                href="{{vacancy.vacancy_url}}"
                target="_blank"
                class="btn btn-primary btn-lg shadow-sm"
              >
                <i class="bi bi-send me-2"></i>Apply Now
              </a>
              <a
                href="{{vacancy.company_url}}"
                target="_blank"
                class="btn btn-outline-primary"
              >
                <i class="bi bi-building me-2"></i>View Company
              </a>
              {% endif %}
            </div>
          </div>
        </div>
    </div>
  </div>
  {% endfor %}
  {% endif %}
</div>
